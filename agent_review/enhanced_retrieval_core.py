import os
import re
import warnings
from dataclasses import dataclass
from typing import Any

import urllib3
from dotenv import load_dotenv
from haystack import Document
from haystack.components.embedders import OpenAIDocumentEmbedder, OpenAITextEmbedder
from haystack.document_stores.types import DuplicatePolicy
from haystack.utils import Secret
from haystack_integrations.components.retrievers.opensearch import (
    OpenSearchHybridRetriever,
)
from haystack_integrations.document_stores.opensearch import OpenSearchDocumentStore

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
warnings.filterwarnings("ignore", message=".*verify_certs=False.*")

load_dotenv(override=True)


@dataclass
class ConversationChunk:
    """对话块数据结构."""
    content: str
    message_ids: list[int]
    start_id: int
    end_id: int
    chunk_type: str  # 'single', 'merged', 'split'
    original_length: int
    meta: dict[str, Any]


class EnhancedRetrievalCore:
    """增强版检索核心类，支持对话合并和长文本分块."""
    
    def __init__(self, 
                 min_content_length: int = 50,
                 max_content_length: int = 1000,
                 merge_window: int = 3,
                 overlap_ratio: float = 0.1):
        """初始化增强版检索核心.
        
        Args:
            min_content_length: 最小内容长度，低于此长度的消息会被合并
            max_content_length: 最大内容长度，超过此长度的消息会被分块
            merge_window: 合并窗口大小，用于合并连续的短消息
            overlap_ratio: 分块时的重叠比例
        """
        self.min_content_length = min_content_length
        self.max_content_length = max_content_length
        self.merge_window = merge_window
        self.overlap_ratio = overlap_ratio
        
        # 初始化存储和嵌入组件
        self._init_components()
    
    def _init_components(self):
        """初始化OpenSearch和嵌入组件."""
        self.doc_store = OpenSearchDocumentStore(
            hosts=f"https://{os.environ['OPENSEARCH_HOST']}",
            http_auth=(
                os.environ["OPENSEARCH_USERNAME"],
                os.environ["OPENSEARCH_PASSWORD"],
            ),
            index=os.environ["OPENSEARCH_INDEX"],
            embedding_dim=os.environ["EMBEDDING_DIMS"],
            verify_certs=False,
            use_ssl=True,
        )

        self.embed_config = {
            "api_base_url": os.environ["EMBEDDING_BASE_URL"],
            "api_key": Secret.from_token(os.environ["EMBEDDING_API_KEY"]),
            "model": os.environ["EMBEDDING_MODEL"],
            "http_client_kwargs": {"verify": False, "timeout": 30.0},
        }

        self.doc_embedder = OpenAIDocumentEmbedder(**self.embed_config)
        self.text_embedder = OpenAITextEmbedder(**self.embed_config)

        self.retriever = OpenSearchHybridRetriever(
            document_store=self.doc_store,
            embedder=self.text_embedder,
            top_k_bm25=5,
            top_k_embedding=5,
            join_mode="reciprocal_rank_fusion",
        )

    def _should_merge_messages(self, messages: list[dict]) -> list[bool]:
        """判断哪些消息应该被合并.
        
        Args:
            messages: 消息列表
            
        Returns:
            布尔列表，指示每个消息是否应该参与合并
        """
        should_merge = []
        
        for msg in messages:
            content = msg.get("msg", "").strip()
            # 短消息、空消息或只包含简单回复的消息应该被合并
            is_short = len(content) < self.min_content_length
            is_simple_reply = bool(re.match(r"^(好的|是的|謝謝|不客气|OK|yes|no)$", content.strip()))
            should_merge.append(is_short or is_simple_reply)
            
        return should_merge

    def _merge_consecutive_messages(self, messages: list[dict]) -> list[ConversationChunk]:  # noqa: C901
        """合并连续的短消息.
        
        Args:
            messages: 原始消息列表
            
        Returns:
            处理后的对话块列表
        """
        if not messages:
            return []
        
        chunks = []
        should_merge = self._should_merge_messages(messages)
        
        i = 0
        while i < len(messages):
            current_msg = messages[i]
            
            # 如果当前消息不需要合并，直接处理
            if not should_merge[i]:
                content = current_msg.get("msg", "").strip()
                if len(content) > self.max_content_length:
                    # 长消息需要分块
                    split_chunks = self._split_long_content(current_msg)
                    chunks.extend(split_chunks)
                else:
                    # 正常长度消息
                    chunk = ConversationChunk(
                        content=content,
                        message_ids=[current_msg.get("id", i)],
                        start_id=current_msg.get("id", i),
                        end_id=current_msg.get("id", i),
                        chunk_type="single",
                        original_length=len(content),
                        meta={k: v for k, v in current_msg.items() if k != "msg"}
                    )
                    chunks.append(chunk)
                i += 1
                continue
            
            # 收集连续的需要合并的消息
            merge_group = []
            merge_ids = []
            j = i
            
            while j < len(messages) and should_merge[j] and len(merge_group) < self.merge_window:
                merge_group.append(messages[j])
                merge_ids.append(messages[j].get("id", j))
                j += 1
            
            # 如果只有一个消息且很短，尝试与前后消息合并
            if len(merge_group) == 1:
                # 尝试与下一个消息合并
                if j < len(messages) and not should_merge[j]:
                    next_msg = messages[j]
                    next_content = next_msg.get("msg", "").strip()
                    if len(next_content) < self.max_content_length:
                        merge_group.append(next_msg)
                        merge_ids.append(next_msg.get("id", j))
                        j += 1
            
            # 创建合并的对话块
            if merge_group:
                merged_content = self._create_merged_content(merge_group)
                
                # 合并元数据
                merged_meta = {}
                for msg in merge_group:
                    for k, v in msg.items():
                        if k != "msg":
                            if k in merged_meta:
                                if isinstance(merged_meta[k], list):
                                    if v not in merged_meta[k]:
                                        merged_meta[k].append(v)
                                else:
                                    if merged_meta[k] != v:
                                        merged_meta[k] = [merged_meta[k], v]
                            else:
                                merged_meta[k] = v
                
                chunk = ConversationChunk(
                    content=merged_content,
                    message_ids=merge_ids,
                    start_id=merge_ids[0],
                    end_id=merge_ids[-1],
                    chunk_type="merged",
                    original_length=sum(len(msg.get("msg", "")) for msg in merge_group),
                    meta=merged_meta
                )
                chunks.append(chunk)
            
            i = j
        
        return chunks

    def _create_merged_content(self, messages: list[dict]) -> str:
        """创建合并后的内容，保持对话的连贯性.
        
        Args:
            messages: 要合并的消息列表
            
        Returns:
            合并后的内容字符串
        """
        if not messages:
            return ""
        
        # 按用户/客服分组合并
        merged_parts = []
        current_speaker = None
        current_content = []
        
        for msg in messages:
            speaker = msg.get("type", "UNKNOWN")
            content = msg.get("msg", "").strip()
            msg_id = msg.get("id", "")
            
            if not content:
                continue
                
            if speaker != current_speaker:
                # 切换说话者，保存之前的内容
                if current_content:
                    speaker_label = "用户" if current_speaker == "USER" else "客服"
                    merged_parts.append(f"{speaker_label}: {' '.join(current_content)}")
                
                current_speaker = speaker
                current_content = [f"[{msg_id}]{content}"]
            else:
                # 同一说话者，继续累加
                current_content.append(f"[{msg_id}]{content}")
        
        # 处理最后的内容
        if current_content:
            speaker_label = "用户" if current_speaker == "USER" else "客服"
            merged_parts.append(f"{speaker_label}: {' '.join(current_content)}")
        
        return " | ".join(merged_parts)

    def _split_long_content(self, message: dict) -> list[ConversationChunk]:
        """分割长内容消息.
        
        Args:
            message: 长消息
            
        Returns:
            分割后的对话块列表
        """
        content = message.get("msg", "").strip()
        if len(content) <= self.max_content_length:
            return [ConversationChunk(
                content=content,
                message_ids=[message.get("id", 0)],
                start_id=message.get("id", 0),
                end_id=message.get("id", 0),
                chunk_type="single",
                original_length=len(content),
                meta={k: v for k, v in message.items() if k != "msg"}
            )]
        
        chunks = []
        overlap_size = int(self.max_content_length * self.overlap_ratio)
        step_size = self.max_content_length - overlap_size
        
        # 尝试按句子边界分割
        sentences = re.split(r"[。！？；\n]", content)
        
        if len(sentences) > 1:
            # 按句子分组
            current_chunk = ""
            current_sentences = []
            
            for sentence in sentences:
                sentence = sentence.strip()
                if not sentence:
                    continue
                    
                # 检查添加这个句子是否会超过长度限制
                test_chunk = current_chunk + sentence + "。" if current_chunk else sentence + "。"
                
                if len(test_chunk) <= self.max_content_length:
                    current_chunk = test_chunk
                    current_sentences.append(sentence)
                else:
                    # 保存当前块
                    if current_chunk:
                        chunk = ConversationChunk(
                            content=current_chunk,
                            message_ids=[message.get("id", 0)],
                            start_id=message.get("id", 0),
                            end_id=message.get("id", 0),
                            chunk_type="split",
                            original_length=len(content),
                            meta={**{k: v for k, v in message.items() if k != "msg"}, 
                                  "chunk_index": len(chunks), "total_chunks": -1}  # total_chunks will be updated later
                        )
                        chunks.append(chunk)
                    
                    # 开始新块
                    current_chunk = sentence + "。"
                    current_sentences = [sentence]
            
            # 处理最后一块
            if current_chunk:
                chunk = ConversationChunk(
                    content=current_chunk,
                    message_ids=[message.get("id", 0)],
                    start_id=message.get("id", 0),
                    end_id=message.get("id", 0),
                    chunk_type="split",
                    original_length=len(content),
                    meta={**{k: v for k, v in message.items() if k != "msg"}, 
                          "chunk_index": len(chunks), "total_chunks": -1}
                )
                chunks.append(chunk)
        else:
            # 按固定长度分割
            for i in range(0, len(content), step_size):
                chunk_content = content[i:i + self.max_content_length]
                
                chunk = ConversationChunk(
                    content=chunk_content,
                    message_ids=[message.get("id", 0)],
                    start_id=message.get("id", 0),
                    end_id=message.get("id", 0),
                    chunk_type="split",
                    original_length=len(content),
                    meta={**{k: v for k, v in message.items() if k != "msg"}, 
                          "chunk_index": len(chunks), "total_chunks": -1}
                )
                chunks.append(chunk)
        
        # 更新total_chunks信息
        for chunk in chunks:
            chunk.meta["total_chunks"] = len(chunks)
        
        return chunks

    def insert_documents_enhanced(self, documents: list[dict], content_key: str = "msg") -> dict[str, Any]:
        """增强版文档插入方法，支持对话合并和长文本分块.
        
        Args:
            documents: 文档列表
            content_key: 内容字段名
            
        Returns:
            插入统计信息
        """
        if not documents:
            return {"total_input": 0, "total_chunks": 0, "merged_count": 0, "split_count": 0}
        
        # 处理消息，生成对话块
        conversation_chunks = self._merge_consecutive_messages(documents)
        
        # 转换为Haystack Document格式
        haystack_docs = []
        stats = {
            "total_input": len(documents),
            "total_chunks": len(conversation_chunks),
            "merged_count": 0,
            "split_count": 0,
            "single_count": 0
        }
        
        for chunk in conversation_chunks:
            # 更新统计信息
            if chunk.chunk_type == "merged":
                stats["merged_count"] += 1
            elif chunk.chunk_type == "split":
                stats["split_count"] += 1
            else:
                stats["single_count"] += 1
            
            # 创建文档元数据
            meta = {
                **chunk.meta,
                "message_ids": chunk.message_ids,
                "start_message_id": chunk.start_id,
                "end_message_id": chunk.end_id,
                "chunk_type": chunk.chunk_type,
                "original_length": chunk.original_length,
                "processed_length": len(chunk.content)
            }
            
            # 重命名id字段避免冲突
            if "id" in meta:
                meta["message_id"] = meta.pop("id")
            
            doc = Document(content=chunk.content, meta=meta)
            haystack_docs.append(doc)
        
        # 执行嵌入和存储
        if haystack_docs:
            docs_with_embeddings = self.doc_embedder.run(documents=haystack_docs)
            self.doc_store.write_documents(docs_with_embeddings["documents"], policy=DuplicatePolicy.SKIP)
        
        return stats

    def search(self, query: str, filters: dict = None, top_k: int = None):
        """搜索文档 - 保持与原始接口兼容."""
        if top_k:
            custom_text_embedder = OpenAITextEmbedder(**self.embed_config)
            custom_retriever = OpenSearchHybridRetriever(
                document_store=self.doc_store,
                embedder=custom_text_embedder,
                top_k_bm25=top_k,
                top_k_embedding=top_k,
                join_mode="reciprocal_rank_fusion",
            )
            results = custom_retriever.run(
                query=query, filters_bm25=filters, filters_embedding=filters
            )
        else:
            results = self.retriever.run(
                query=query, filters_bm25=filters, filters_embedding=filters
            )

        documents = results["documents"]

        # 按message_id排序
        documents.sort(key=lambda doc: doc.meta.get("start_message_id", 0))

        # 应用分数阈值过滤
        if documents:
            top_score = max(doc.score for doc in documents)
            score_threshold = top_score * 0.6
            documents = [doc for doc in documents if doc.score >= score_threshold]

        return documents

    def delete_all_documents(self):
        """删除所有文档."""
        documents = self.doc_store.filter_documents()
        for doc in documents:
            self.doc_store.delete_documents([doc.id])

    # 为了兼容性，保留原始方法
    def insert_documents(self, documents: list[dict], content_key: str = "msg"):
        """保持向后兼容的简单插入方法."""
        return self.insert_documents_enhanced(documents, content_key)


if __name__ == "__main__":
    # 测试代码
    test_docs = [
        {"msg": "你好", "id": 1, "type": "USER", "caseId": "test"},
        {"msg": "您好，欢迎使用我们的服务", "id": 2, "type": "AGENT", "caseId": "test"},
        {"msg": "好的", "id": 3, "type": "USER", "caseId": "test"},
        {"msg": "请问您需要什么帮助？" * 50, "id": 4, "type": "AGENT", "caseId": "test"},  # 长文本
        {"msg": "谢谢", "id": 5, "type": "USER", "caseId": "test"},
    ]

    enhanced_kb = EnhancedRetrievalCore()
    stats = enhanced_kb.insert_documents_enhanced(test_docs)
    print("插入统计:", stats)
    
    # 测试搜索
    results = enhanced_kb.search("帮助")
    print(f"搜索结果数量: {len(results)}")
    for doc in results:
        print(f"内容: {doc.content[:50]}...")
        print(f"元数据: {doc.meta}")
        print("---")