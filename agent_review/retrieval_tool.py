from smolagents import Tool

from agent_review.retrieval_core import RetrievalCore


class RetrieverTool(Tool):
    name = "retriever"
    description = "Uses semantic search to retrieve relevant parts of user conversation history that could help answer your query."
    inputs = {
        "query": {
            "type": "string",
            "description": "The query to search conversation history. This should be semantically close to the information you're looking for in past conversations. Use the affirmative form rather than a question.",
        },
        "category": {
            "type": "string",
            "description": "The sensitive content category to focus the search on. Options: consulting_company_info, selling_user_info, negative_news, major_complaints, request_contact_information, spam_messages",
            # "nullable": True,
        },
    }
    output_type = "string"

    def __init__(self, knowledge_base: RetrievalCore = RetrievalCore()):
        super().__init__()
        self.knowledge_base = knowledge_base

        # Category-specific task descriptions for better retrieval
        self.category_tasks = {
            "consulting_company_info": "Find messages where users ask about company location, address, servers, or business registration details",
            "selling_user_info": "Find messages offering to buy, sell, or trade user data or account information",
            "negative_news": "Find messages about platform rumors, security concerns, bankruptcy claims, or negative publicity",
            "major_complaints": "Find messages showing extreme user anger, threats of legal action, or public exposure intentions",
            "request_contact_information": "Find messages where staff ask users for personal contact details like email, phone, or social media",
            "spam_messages": "Find messages from staff containing inappropriate, offensive, or unprofessional language",
        }

    @staticmethod
    def get_detailed_instruct(task_description: str, query: str) -> str:
        return f"Instruct: {task_description}\nQuery: {query}"

    def forward(self, query: str, category: str) -> str:
        """Execute the retrieval based on the provided query and category."""
        assert isinstance(query, str), "Your search query must be a string"

        enhanced_query = self.get_detailed_instruct(
            query=query, task_description=self.category_tasks[category]
        )

        final_docs = self.knowledge_base.search(enhanced_query)

        result_lines = ["Retrieved documents:"]
        for i, doc in enumerate(final_docs):
            result_lines.append(f"Document {i}:")
            result_lines.append(f"  Content: {doc.content}")
            result_lines.append(
                f"  Message_ID: {doc.meta.get('message_id', 'unknown')}"
            )
            result_lines.append(f"  Type: {doc.meta.get('type', 'unknown')}")
            result_lines.append(f"  Score: {doc.score:.4f}")
            if hasattr(doc, "boosted_score") and category:
                result_lines.append(f"  Category_Relevance: {doc.boosted_score:.4f}")
            result_lines.append("")  # Empty line for separation

        return "\n".join(result_lines)


if __name__ == "__main__":
    retriever_tool = RetrieverTool()
    print(retriever_tool(query="公司在哪里", category="consulting_company_info"))
